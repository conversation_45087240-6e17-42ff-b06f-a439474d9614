import { m } from 'framer-motion';

import { Button, SxProps, Typography } from '@mui/material';

import { MotionContainer, varBounce } from '../animate';
import { paths } from 'src/routes/paths';

export interface ErrorScreenProps {
  title?: string;
  message?: string;
  action?: React.ReactNode;
  sx?: SxProps;
}

export const ErrorScreen: React.FC<ErrorScreenProps> = ({
  title = 'Something wrong happened',
  message = 'There was an error, please try again later.',
  action,
  sx,
}) => {
  return (
    <MotionContainer
      sx={{
        px: 5,
        width: 1,
        flexGrow: 1,
        minHeight: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        ...sx,
      }}
    >
      <m.div variants={varBounce().in}>
        <Typography color="primary" variant="h3" sx={{ mb: 2 }}>
          {title}
        </Typography>
      </m.div>

      <m.div variants={varBounce().in}>
        <Typography sx={{ color: 'text.secondary', mb: 2 }}>{message}</Typography>
      </m.div>

      {action ?? (
        <Button component="a" href={paths.dashboard.root} size="large" variant="contained">
          Refresh
        </Button>
      )}
    </MotionContainer>
  );
};

export default ErrorScreen;
