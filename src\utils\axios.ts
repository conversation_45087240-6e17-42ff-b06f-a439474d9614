import type { AxiosRequestConfig } from 'axios';

import axios from 'axios';
import { exchangeOAuthCode, refreshToken } from 'src/auth/context/jwt';

import { CONFIG } from 'src/config-global';

// ----------------------------------------------------------------------

const axiosInstance = axios.create({ baseURL: CONFIG.site.WorkforcesServerUrl });

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    console.log('erorr', error);
    if (error.response?.status == 401) {
      refreshToken?.();
    }
    Promise.reject((error.response && error.response.data) || 'Something went wrong!');
  }
);

export default axiosInstance;

// ----------------------------------------------------------------------

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
  try {
    const [url, config] = Array.isArray(args) ? args : [args];

    const res = await axiosInstance.get(url, { ...config });

    return res.data;
  } catch (error) {
    console.error('Failed to fetch:', error);
    throw error;
  }
};

// ----------------------------------------------------------------------

export const endpoints = {
  auth: {
    signIn: '/auth/login/basic',
    signUp: '/auth/register',
    refreshToken: '/auth/refresh-token',
    forgetPassword: '/auth/request-password-change',
    resetPassword: '/auth/reset-password',
    exchangeOAuthCode: '/auth/login/exchange-code',
  },
  user: {
    me: '/users/me',
  },
};
